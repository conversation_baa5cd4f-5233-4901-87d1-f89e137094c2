import { ref, computed, onMounted, watch, nextTick } from 'vue';
import { useStore } from 'vuex';
import { useRouter } from 'vue-router';
import { useHealthMetrics } from '@/composables/useHealthMetrics';
import { useReadings } from './useReadings';
import { useFormatters } from '@/composables/useFormatters';

/**
 * Composable for patient dashboard functionality
 * @returns {Object} Dashboard utility functions and state
 * Last updated: 2025-01-09 - Fixed requestNewReading to navigate instead of calling store
 * Cache buster: v2.0 - Fixed REST.SET error by ensuring no store calls
 */
export function useDashboard() {
  const store = useStore();
  const router = useRouter();
  const { formatDate, formatDecimal } = useFormatters();

  // State
  const lastReading = ref(null);
  const latestMetrics = ref({
    respiratoryRate: '--',
    oxygenSaturation: '--',
    heartRate: '--',
    temperature: '--'
  });
  const isReadingInProgress = ref(false);
  const error = ref(null);

  // History data
  const respiratoryRateHistory = ref([]);
  const spirometryHistory = ref([]);
  const oxygenSaturationHistory = ref([]);
  const heartRateHistory = ref([]);
  const temperatureHistory = ref([]);

  // Get readings functionality
  const readingsComposable = useReadings();
  console.log('useDashboard - readingsComposable:', Object.keys(readingsComposable));
  console.log('useDashboard - setViewMode type:', typeof readingsComposable.setViewMode);

  const {
    loadingMore,
    recentReadings,
    hasMoreReadings,
    isDownloading,
    viewMode,
    maxReadings,
    isInitialized,
    isInitializing,
    loadMoreReadings,
    initializeReadings,
    resetReadings,
    downloadHistory,
    setViewMode
  } = readingsComposable;

  // Add debugging for viewMode
  console.log('useDashboard - Initial viewMode:', viewMode.value);

  // Reference to the calendar component
  const calendarComponent = ref(null);

  // Get health metrics functionality
  const {
    copdRiskLevel,
    getStatusIcon,
    getStatusMessage,
    getMetricIcon,
    getSpirometryDisplay,
    calculateRespiratoryRateFromSpirometry,
    metrics
  } = useHealthMetrics({ lastReading });

  /**
   * Format metrics for display - respiratory rate now calculated from spirometry
   */
  const formattedMetrics = computed(() => {
    const latest = latestMetrics.value;

    // Calculate respiratory rate from spirometry data if available
    let respiratoryRateValue = '--';
    if (latest.spirometry) {
      const calculatedRR = calculateRespiratoryRateFromSpirometry(latest.spirometry);
      respiratoryRateValue = calculatedRR ? `${calculatedRR} bpm` : '--';
    } else if (latest.respiratoryRate && latest.respiratoryRate !== null) {
      // Fallback to original respiratory rate if no spirometry data
      respiratoryRateValue = formatDecimal(latest.respiratoryRate) + ' bpm';
    }

    return {
      respiratoryRate: respiratoryRateValue,
      oxygenSaturation: formatDecimal(latest.oxygenSaturation) + '%',
      heartRate: formatDecimal(latest.heartRate) + ' bpm',
      temperature: formatDecimal(latest.temperature) + '°C'
    };
  });

  /**
   * Fetch dashboard data
   */
  const fetchDashboardData = async () => {
    try {
      error.value = null;
      const data = await store.dispatch('patient/health/fetchHealthData');

      if (!data) {
        console.warn('No data received from fetchHealthData');
        // Reset all values to defaults
        lastReading.value = null;
        latestMetrics.value = {};
        // DON'T reset recentReadings here - let useReadings handle it
        respiratoryRateHistory.value = [];
        oxygenSaturationHistory.value = [];
        heartRateHistory.value = [];
        temperatureHistory.value = [];
        return;
      }

      lastReading.value = data.lastReading;
      latestMetrics.value = data.latestMetrics;
      // DON'T overwrite recentReadings here - let useReadings manage its own state

      // Debug: Log the data fetched from the API
      console.log('Fetched dashboard data:', data);
      console.log('Last reading:', data.lastReading);
      console.log('Latest metrics:', data.latestMetrics);
      console.log('Recent readings from API (not used):', data.recentReadings);

      if (data.history) {
        respiratoryRateHistory.value = data.history.respiratoryRate;
        spirometryHistory.value = data.history.spirometry || [];
        oxygenSaturationHistory.value = data.history.oxygenSaturation;
        heartRateHistory.value = data.history.heartRate;
        temperatureHistory.value = data.history.temperature;
      }
    } catch (err) {
      error.value = 'Failed to load dashboard data';
      console.error('Error loading dashboard data:', err);
    }
  };

  /**
   * Request a new reading - Navigate to reading capture
   * FIXED: No longer calls store action, just navigates
   */
  const requestNewReading = async () => {
    try {
      console.log('Navigating to reading capture page...');
      router.push('/patient/reading-capture');
    } catch (error) {
      console.error('Error in requestNewReading:', error);
      // Fallback: just navigate without any store calls
      router.push('/patient/reading-capture');
    }
  };

  /**
   * Navigate to detailed report
   */
  const viewDetailedReport = () => {
    router.push('/patient/detailed-report');
  };

  /**
   * Download health history
   */
  const handleDownloadHistory = async () => {
    await downloadHistory(lastReading.value, copdRiskLevel.value);
  };

  /**
   * Navigate to contact practitioner
   */
  const navigateToPractitioners = () => {
    router.push('/patient/profile');
    // Set the activeTab to 'practitioners' in localStorage so the Profile component can use it
    localStorage.setItem('profileActiveTab', 'practitioners');
  };

  /**
   * Get history data for a specific metric
   * @param {string} key - The metric key
   * @returns {Array} History data for the metric
   */
  const getHistoryData = (key) => {
    switch(key) {
      case 'respiratoryRate':
        // For respiratory rate, calculate from spirometry data if available
        if (spirometryHistory.value.length > 0) {
          return spirometryHistory.value.map(reading => {
            const calculatedRR = calculateRespiratoryRateFromSpirometry(reading.spirometry);
            return {
              x: reading.timestamp,
              y: calculatedRR || 16 // Default to 16 if calculation fails
            };
          });
        }
        // Fallback to original respiratory rate history
        return respiratoryRateHistory.value;
      case 'oxygenSaturation':
        return oxygenSaturationHistory.value;
      case 'heartRate':
        return heartRateHistory.value;
      case 'temperature':
        return temperatureHistory.value;
      default:
        return [];
    }
  };

  // Watch for changes in view mode
  watch(viewMode, async (newMode, oldMode) => {
    console.log(`View mode changed from ${oldMode} to ${newMode}`);

    if (newMode === 'calendar' && oldMode === 'list') {
      console.log('Switched from list to calendar view, refreshing calendar data');
      // Wait for the calendar component to be mounted
      await nextTick();
      // If we have a reference to the calendar component, refresh its data
      if (calendarComponent.value) {
        if (calendarComponent.value.refreshCalendarData) {
          calendarComponent.value.refreshCalendarData();
        } else if (calendarComponent.value.fetchCalendarReadings) {
          calendarComponent.value.fetchCalendarReadings();
        }
      }
    }
    else if (newMode === 'list' && oldMode === 'calendar') {
      console.log('Switched from calendar to list view, checking if readings need initialization');

      // Only initialize if not already initialized and not currently initializing - prevent infinite loop
      if (!isInitialized.value && !isInitializing.value) {
        console.log('List view selected but readings not initialized, initializing...');
        await initializeReadings();
      } else if (isInitializing.value) {
        console.log('List view selected but readings are currently initializing, skipping');
      } else {
        console.log('List view selected and readings already initialized, skipping initialization');
      }
    }
  });

  // Initialize data
  onMounted(async () => {
    console.log('useDashboard - Component mounted, initializing...');

    // First, fetch the dashboard data (metrics, history, etc.)
    await fetchDashboardData();

    // Only initialize readings if we're starting in list view
    // The calendar view will handle its own data loading
    if (viewMode.value === 'list') {
      console.log('useDashboard - Starting in list view, initializing readings...');
      await initializeReadings();
    } else {
      console.log('useDashboard - Starting in calendar view, skipping readings initialization');
    }
  });



  return {
    // State
    lastReading,
    latestMetrics,
    recentReadings,
    copdRiskLevel,
    isReadingInProgress,
    isDownloading,
    error,
    loadingMore,
    hasMoreReadings,
    viewMode,
    calendarComponent,

    // Computed
    getStatusIcon,
    getStatusMessage,
    getMetricIcon,
    formattedMetrics,

    // Data
    respiratoryRateHistory,
    spirometryHistory,
    oxygenSaturationHistory,
    heartRateHistory,
    temperatureHistory,
    metrics,
    getSpirometryDisplay,

    // Methods
    requestNewReading,
    viewDetailedReport,
    downloadHistory: handleDownloadHistory,
    navigateToPractitioners,
    formatDate,
    formatDecimal,
    getHistoryData,
    loadMoreReadings,
    setViewMode,
    resetReadings,
    calculateRespiratoryRateFromSpirometry,

    // Additional state for debugging
    maxReadings,
    isInitialized,
    isInitializing
  };
}
