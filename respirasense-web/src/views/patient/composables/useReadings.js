import { ref, computed } from 'vue';
import { useStore } from 'vuex';
import { format } from 'date-fns';
import jsPDF from 'jspdf';
import 'jspdf-autotable';
import { useFormatters } from '@/composables/useFormatters';

/**
 * Composable for readings functionality
 * @returns {Object} Readings utility functions and state
 */
export function useReadings() {
  const store = useStore();
  const { formatDate, formatDecimal, formatDateTime } = useFormatters();

  // State
  const loadingMore = ref(false);
  const recentReadings = ref([]);
  const hasMoreReadings = ref(true);
  const readingsPerPage = ref(5);
  const isDownloading = ref(false);
  const viewMode = ref('calendar');
  const error = ref(null);

  // Add safety limits to prevent endless loading
  const maxReadings = ref(100); // Maximum number of readings to load
  const isInitialized = ref(false);

  // Throttle mechanism to prevent rapid successive calls
  const lastLoadTime = ref(0);
  const loadThrottleMs = ref(1000); // Minimum time between load calls

  /**
   * Load more readings
   */
  const loadMoreReadings = async () => {
    // Enhanced safety checks to prevent endless loading
    if (loadingMore.value || !hasMoreReadings.value) {
      console.log('loadMoreReadings - Skipping: loadingMore =', loadingMore.value, 'hasMoreReadings =', hasMoreReadings.value);
      return;
    }

    // Throttle mechanism to prevent rapid successive calls
    const now = Date.now();
    if (now - lastLoadTime.value < loadThrottleMs.value) {
      console.log('loadMoreReadings - Throttled: too soon since last call');
      return;
    }
    lastLoadTime.value = now;

    // Check if we've reached the maximum number of readings
    if (recentReadings.value && recentReadings.value.length >= maxReadings.value) {
      console.log('loadMoreReadings - Maximum readings limit reached:', maxReadings.value);
      hasMoreReadings.value = false;
      return;
    }

    try {
      loadingMore.value = true;
      console.log('loadMoreReadings - Starting to load more readings...');

      // Ensure recentReadings is initialized as an array
      if (!recentReadings.value || !Array.isArray(recentReadings.value)) {
        console.log('loadMoreReadings - recentReadings is not initialized, initializing as empty array');
        recentReadings.value = [];
      }

      // Get the last reading if available
      const lastReading = recentReadings.value.length > 0 ?
        recentReadings.value[recentReadings.value.length - 1] : null;
      const lastTimestamp = lastReading ? (lastReading.timestamp || lastReading.lastUpdated) : null;

      console.log('loadMoreReadings - Current readings count:', recentReadings.value.length);
      console.log('loadMoreReadings - Last timestamp:', lastTimestamp);

      const moreReadings = await store.dispatch('patient/health/fetchMoreReadings', {
        limit: readingsPerPage.value,
        lastTimestamp
      });

      console.log('loadMoreReadings - Received readings:', moreReadings?.length || 0);

      // Make sure moreReadings is an array before using it
      if (moreReadings && Array.isArray(moreReadings)) {
        // If we received fewer readings than requested, we've reached the end
        if (moreReadings.length < readingsPerPage.value) {
          console.log('loadMoreReadings - Reached end of readings');
          hasMoreReadings.value = false;
        }

        if (moreReadings.length > 0) {
          // Check if adding these readings would exceed our limit
          const newTotal = recentReadings.value.length + moreReadings.length;
          if (newTotal > maxReadings.value) {
            // Only add readings up to the limit
            const remainingSlots = maxReadings.value - recentReadings.value.length;
            const limitedReadings = moreReadings.slice(0, remainingSlots);
            recentReadings.value = [...recentReadings.value, ...limitedReadings];
            hasMoreReadings.value = false;
            console.log('loadMoreReadings - Limited readings to prevent exceeding maximum');
          } else {
            recentReadings.value = [...recentReadings.value, ...moreReadings];
          }
          console.log('loadMoreReadings - New total readings:', recentReadings.value.length);
        } else {
          console.log('loadMoreReadings - No new readings received');
          hasMoreReadings.value = false;
        }
      } else {
        console.warn('fetchMoreReadings did not return an array:', moreReadings);
        hasMoreReadings.value = false;
      }
    } catch (err) {
      error.value = 'Failed to load more readings';
      console.error('Error loading more readings:', err);
      hasMoreReadings.value = false;
    } finally {
      loadingMore.value = false;
      console.log('loadMoreReadings - Finished. hasMoreReadings:', hasMoreReadings.value);
    }
  };

  /**
   * Initialize readings
   */
  const initializeReadings = async () => {
    // Prevent multiple initializations
    if (isInitialized.value) {
      console.log('initializeReadings - Already initialized, skipping');
      return;
    }

    try {
      console.log('initializeReadings - Starting initialization...');
      isInitialized.value = true;

      // Ensure recentReadings is initialized as an array
      if (!recentReadings.value || !Array.isArray(recentReadings.value)) {
        recentReadings.value = [];
      }

      // Reset state
      hasMoreReadings.value = true;
      error.value = null;

      const initialReadings = await store.dispatch('patient/health/fetchMoreReadings', {
        limit: readingsPerPage.value
      });

      // Debug: Log the initial readings
      console.log('initializeReadings - Initial readings loaded:', initialReadings?.length || 0);

      // Make sure initialReadings is an array before assigning
      if (initialReadings && Array.isArray(initialReadings)) {
        recentReadings.value = initialReadings;
        hasMoreReadings.value = initialReadings.length === readingsPerPage.value;
        console.log('initializeReadings - Set hasMoreReadings to:', hasMoreReadings.value);
      } else {
        console.warn('initializeReadings - fetchMoreReadings did not return an array:', initialReadings);
        recentReadings.value = [];
        hasMoreReadings.value = false;
      }
    } catch (err) {
      error.value = 'Failed to load initial readings';
      console.error('Error loading initial readings:', err);
      // Ensure recentReadings is an array even if there's an error
      recentReadings.value = [];
      hasMoreReadings.value = false;
    }
  };

  /**
   * Reset readings state - useful for cleanup
   */
  const resetReadings = () => {
    console.log('resetReadings - Resetting readings state');
    recentReadings.value = [];
    hasMoreReadings.value = true;
    loadingMore.value = false;
    isInitialized.value = false;
    error.value = null;
    lastLoadTime.value = 0; // Reset throttle timer
  };

  /**
   * Set the view mode (calendar or list)
   * @param {string} mode - The view mode to set ('calendar' or 'list')
   */
  const setViewMode = (mode) => {
    if (mode !== 'calendar' && mode !== 'list') {
      console.error(`Invalid view mode: ${mode}. Must be 'calendar' or 'list'`);
      return;
    }

    console.log(`useReadings - Switching view mode from ${viewMode.value} to ${mode}`);

    // Ensure recentReadings is initialized as an array
    if (!recentReadings.value || !Array.isArray(recentReadings.value)) {
      console.log('useReadings - recentReadings is not initialized, initializing as empty array');
      recentReadings.value = [];
    }

    // Set the view mode
    viewMode.value = mode;

    // If switching to list view, make sure we have data
    if (mode === 'list' && recentReadings.value.length === 0) {
      console.log('useReadings - Switching to list view with no readings, will initialize readings');
      // We'll let the watcher handle this to avoid async issues in the setter
    }

    // Verify the change
    console.log(`useReadings - After change, viewMode is now: ${viewMode.value}`);
  };

  /**
   * Download health history as PDF
   */
  const downloadHistory = async (lastReading, copdRiskLevel) => {
    try {
      isDownloading.value = true;
      const response = await store.dispatch('patient/health/downloadHealthHistory');

      if (!response || !response.readings || !response.readings.length) {
        throw new Error('No readings data available for download');
      }

      // Generate PDF report
      generatePDF(response.readings, lastReading, copdRiskLevel);
    } catch (error) {
      console.error('Error downloading history:', error);
      // You might want to show this error to the user
      error.value = 'Failed to download health history';
    } finally {
      isDownloading.value = false;
    }
  };

  /**
   * Generate PDF report
   * @param {Array} readings - The readings data
   * @param {Object} lastReading - The last reading
   * @param {string} copdRiskLevel - The COPD risk level
   */
  const generatePDF = (readings, lastReading, copdRiskLevel) => {
    const doc = new jsPDF();

    // Add title
    doc.setFontSize(20);
    doc.text('Health History Report', 105, 15, { align: 'center' });

    // Add date
    doc.setFontSize(12);
    try {
      doc.text(`Generated on: ${format(new Date(), 'MMMM d, yyyy')}`, 105, 25, { align: 'center' });
    } catch (error) {
      console.error('Error formatting generation date:', error);
      doc.text(`Generated on: ${new Date().toLocaleDateString()}`, 105, 25, { align: 'center' });
    }

    // Add patient info if available
    if (lastReading && lastReading.patientName) {
      doc.text(`Patient: ${lastReading.patientName}`, 14, 35);
    }

    // Add summary statistics
    doc.setFontSize(16);
    doc.text('Summary Statistics', 14, 45);

    doc.setFontSize(12);
    doc.text(`Total Readings: ${readings.length}`, 20, 55);
    try {
      doc.text(`Latest Reading: ${formatDate(lastReading?.timestamp || lastReading?.lastUpdated || new Date())}`, 20, 65);
    } catch (error) {
      console.error('Error formatting latest reading date:', error);
      doc.text(`Latest Reading: ${new Date().toLocaleDateString()}`, 20, 65);
    }
    doc.text(`COPD Risk Level: ${copdRiskLevel.toUpperCase()}`, 20, 75);

    // Add readings table
    doc.setFontSize(16);
    doc.text('Readings Data', 14, 90);

    const tableData = readings.map(reading => {
      try {
        return [
          formatDate(reading.timestamp || reading.lastUpdated),
          formatDecimal(reading.respiratoryRate) + ' bpm',
          formatDecimal(reading.oxygenSaturation) + '%',
          formatDecimal(reading.heartRate) + ' bpm',
          formatDecimal(reading.temperature) + '\u00b0C',
          (reading.riskLevel || 'normal').toUpperCase()
        ];
      } catch (error) {
        console.error('Error formatting reading data:', error);
        return [
          'Invalid date',
          formatDecimal(reading.respiratoryRate) + ' bpm',
          formatDecimal(reading.oxygenSaturation) + '%',
          formatDecimal(reading.heartRate) + ' bpm',
          formatDecimal(reading.temperature) + '\u00b0C',
          (reading.riskLevel || 'normal').toUpperCase()
        ];
      }
    });

    // Check if we have spirometry data to determine table headers
    const hasSpirometry = readings.some(reading => reading.spirometry);

    const headers = hasSpirometry
      ? [['Date & Time', 'FEV₁ (L)', 'FVC (L)', 'FEV₁/FVC (%)', 'Oxygen Sat.', 'Heart Rate', 'Temperature', 'COPD Risk']]
      : [['Date & Time', 'Respiratory Rate', 'Oxygen Saturation', 'Heart Rate', 'Temperature', 'COPD Risk']];

    // Update table data to include spirometry if available
    const enhancedTableData = readings.map(reading => {
      const baseData = [
        format(reading.timestamp, 'MMM d, yyyy HH:mm'),
        reading.oxygenSaturation ? `${reading.oxygenSaturation.toFixed(1)}%` : '--',
        reading.heartRate ? `${reading.heartRate.toFixed(0)} bpm` : '--',
        reading.temperature ? `${reading.temperature.toFixed(1)}°C` : '--',
        (reading.riskLevel || 'normal').toUpperCase()
      ];

      if (hasSpirometry && reading.spirometry) {
        return [
          format(reading.timestamp, 'MMM d, yyyy HH:mm'),
          reading.spirometry.FEV1 ? `${reading.spirometry.FEV1.toFixed(2)}` : '--',
          reading.spirometry.FVC ? `${reading.spirometry.FVC.toFixed(2)}` : '--',
          reading.spirometry.FEV1_FVC ? `${(reading.spirometry.FEV1_FVC * 100).toFixed(1)}` : '--',
          ...baseData.slice(1)
        ];
      } else if (!hasSpirometry) {
        return [
          format(reading.timestamp, 'MMM d, yyyy HH:mm'),
          reading.respiratoryRate ? `${reading.respiratoryRate.toFixed(0)} bpm` : '--',
          ...baseData.slice(1)
        ];
      } else {
        // Has spirometry but this reading doesn't - show empty spirometry columns
        return [
          format(reading.timestamp, 'MMM d, yyyy HH:mm'),
          '--', '--', '--',
          ...baseData.slice(1)
        ];
      }
    });

    doc.autoTable({
      startY: 95,
      head: headers,
      body: enhancedTableData,
      theme: 'striped',
      headStyles: { fillColor: [183, 21, 64] },
      styles: { fontSize: 9 },
      columnStyles: hasSpirometry ? {
        0: { cellWidth: 35 },
        1: { cellWidth: 20 },
        2: { cellWidth: 20 },
        3: { cellWidth: 25 },
        4: { cellWidth: 25 },
        5: { cellWidth: 25 },
        6: { cellWidth: 25 },
        7: { cellWidth: 25 }
      } : undefined
    });

    // Save the PDF
    try {
      doc.save(`health_history_${format(new Date(), 'yyyy-MM-dd')}.pdf`);
    } catch (error) {
      console.error('Error formatting PDF filename:', error);
      doc.save(`health_history_${new Date().toISOString().split('T')[0]}.pdf`);
    }
  };

  return {
    loadingMore,
    recentReadings,
    hasMoreReadings,
    readingsPerPage,
    isDownloading,
    viewMode,
    error,
    maxReadings,
    isInitialized,
    loadMoreReadings,
    initializeReadings,
    resetReadings,
    downloadHistory,
    setViewMode
  };
}
